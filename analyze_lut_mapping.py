#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析LUT表映射问题：为什么117A的52万微伏会被映射到106A
"""

import numpy as np
import matplotlib.pyplot as plt

# 您提供的LUT表数据
UV_LUT_X = [19426, 51059, 78886, 161315, 224188, 270587, 339759, 411886, 475809, 573398, 631120, 711089, 777964, 809663]
UV_LUT_Y = [30535, 64674, 89394, 146203, 188653, 229566, 304495, 386124, 456978, 569395, 634034, 723603, 784924, 789318]

def correct_uv_python(raw_uv):
    """Python版本的correct_uv函数，与您的C代码逻辑相同"""
    lut_x = UV_LUT_X
    lut_y = UV_LUT_Y
    lut_size = len(lut_x)
    
    if lut_size < 2:
        return raw_uv
    
    if raw_uv <= lut_x[0]:
        return lut_y[0]
    if raw_uv >= lut_x[lut_size-1]:
        return lut_y[lut_size-1]
    
    for i in range(lut_size - 1):
        if raw_uv >= lut_x[i] and raw_uv <= lut_x[i+1]:
            x0, x1 = lut_x[i], lut_x[i+1]
            y0, y1 = lut_y[i], lut_y[i+1]
            if x1 == x0:
                return y0
            return y0 + (y1 - y0) * (raw_uv - x0) // (x1 - x0)  # 整数除法
    
    return raw_uv

def analyze_mapping():
    """分析52万微伏的映射过程"""
    raw_uv = 520000  # 52万微伏
    
    print("=== LUT表映射分析 ===")
    print(f"输入原始微伏值: {raw_uv}")
    print()
    
    # 显示LUT表
    print("LUT表数据:")
    print("索引  X(原始uV)    Y(目标uV)")
    for i, (x, y) in enumerate(zip(UV_LUT_X, UV_LUT_Y)):
        print(f"{i:2d}   {x:8d}     {y:8d}")
    print()
    
    # 查找插值区间
    lut_x = UV_LUT_X
    lut_y = UV_LUT_Y
    lut_size = len(lut_x)
    
    print(f"查找 {raw_uv} 在LUT表中的位置:")
    
    if raw_uv <= lut_x[0]:
        print(f"  {raw_uv} <= {lut_x[0]} (第一个点)")
        result = lut_y[0]
        print(f"  直接返回第一个Y值: {result}")
    elif raw_uv >= lut_x[lut_size-1]:
        print(f"  {raw_uv} >= {lut_x[lut_size-1]} (最后一个点)")
        result = lut_y[lut_size-1]
        print(f"  直接返回最后一个Y值: {result}")
    else:
        for i in range(lut_size - 1):
            if raw_uv >= lut_x[i] and raw_uv <= lut_x[i+1]:
                x0, x1 = lut_x[i], lut_x[i+1]
                y0, y1 = lut_y[i], lut_y[i+1]
                print(f"  找到插值区间: 索引{i}到{i+1}")
                print(f"  X区间: [{x0}, {x1}]")
                print(f"  Y区间: [{y0}, {y1}]")
                print(f"  插值计算:")
                print(f"    比例 = ({raw_uv} - {x0}) / ({x1} - {x0}) = {(raw_uv - x0) / (x1 - x0):.6f}")
                
                # 使用浮点数计算
                result_float = y0 + (y1 - y0) * (raw_uv - x0) / (x1 - x0)
                print(f"    浮点结果 = {y0} + ({y1} - {y0}) * {(raw_uv - x0) / (x1 - x0):.6f} = {result_float:.1f}")
                
                # 使用整数计算（模拟C代码）
                result_int = y0 + (y1 - y0) * (raw_uv - x0) // (x1 - x0)
                print(f"    整数结果 = {y0} + ({y1} - {y0}) * {(raw_uv - x0)} // {(x1 - x0)} = {result_int}")
                
                result = result_int
                break
    
    print()
    print(f"最终校正后的微伏值: {result}")
    
    # 如果有电流换算关系，计算对应的电流
    # 假设有一个微伏到电流的换算关系（需要您提供具体的换算公式）
    print()
    print("=== 电流换算分析 ===")
    print("需要您提供微伏值到电流的换算公式才能分析为什么会从117A变成106A")
    
    return result

def plot_lut_curve():
    """绘制LUT曲线"""
    plt.figure(figsize=(12, 8))
    
    # 绘制LUT点
    plt.subplot(2, 1, 1)
    plt.plot(UV_LUT_X, UV_LUT_Y, 'ro-', label='LUT Points')
    plt.axvline(x=520000, color='red', linestyle='--', label='520000 µV (输入)')
    plt.xlabel('原始微伏值 (µV)')
    plt.ylabel('校正后微伏值 (µV)')
    plt.title('LUT校正曲线')
    plt.grid(True)
    plt.legend()
    
    # 绘制校正前后的对比
    plt.subplot(2, 1, 2)
    raw_range = np.linspace(0, 900000, 1000)
    corrected_range = [correct_uv_python(uv) for uv in raw_range]
    plt.plot(raw_range, corrected_range, 'b-', label='校正曲线')
    plt.plot(raw_range, raw_range, 'g--', alpha=0.5, label='y=x (无校正)')
    plt.axvline(x=520000, color='red', linestyle='--', label='520000 µV')
    plt.axhline(y=correct_uv_python(520000), color='red', linestyle='--', alpha=0.7)
    plt.xlabel('原始微伏值 (µV)')
    plt.ylabel('校正后微伏值 (µV)')
    plt.title('校正效果对比')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('lut_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    result = analyze_mapping()
    plot_lut_curve()
